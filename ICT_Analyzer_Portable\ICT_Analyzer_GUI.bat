@echo off
title ICT Analyzer GUI Launcher
echo.
echo ========================================
echo    ICT Analyzer GUI Launcher v1.0
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if the GUI file exists
if not exist "ict_analyzer_gui.py" (
    echo ERROR: ict_analyzer_gui.py not found in current directory
    echo Please ensure all files are in the same folder
    echo.
    pause
    exit /b 1
)

REM Check if required modules exist
if not exist "config.py" (
    echo ERROR: config.py not found
    echo Please ensure all ICT Analyzer files are present
    echo.
    pause
    exit /b 1
)

echo Starting ICT Analyzer GUI...
echo.

REM Launch the GUI application
python ict_analyzer_gui.py

REM Check if the application exited with an error
if errorlevel 1 (
    echo.
    echo ERROR: Application exited with an error
    echo Check the logs folder for more details
    echo.
    pause
)

echo.
echo ICT Analyzer GUI has closed.
pause
