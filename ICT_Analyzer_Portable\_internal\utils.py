# utils.py
"""
ماژول توابع کمکی عمومی
General utility functions.
"""
import base64
import pathlib
import re
import html
import logging
from typing import Tuple # For type hinting

# Import FPDF if available, and other necessary libraries for specific functions
try:
    from fpdf import FPDF
except ImportError:
    FPDF = None # Placeholder if fpdf is not available

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
except ImportError:
    arabic_reshaper = None
    get_display = None

# Import LIBRARIES_LOADED_SUCCESSFULLY from config to check dependencies
import config

logger = logging.getLogger(__name__)

def split_long_words(txt: str, max_width: float, pdf_obj: 'FPDF') -> str:
    """
    Splits long words to fit within a given width in an FPDF object.
    تقسیم کلمات طولانی برای جا شدن در عرض مشخص در شیء FPDF.
    """
    if not config.LIBRARIES_LOADED_SUCCESSFULLY or not FPDF or not arabic_reshaper or not get_display:
        logger.warning("FPDF, arabic_reshaper, or bidi not available for split_long_words. Returning original text.")
        return txt
    if not txt.strip():
        return txt
    words = txt.split(' ')
    output_words = []
    soft_hyphen = '\u00AD'  # Soft hyphen

    for word in words:
        try:
            if not hasattr(pdf_obj, 'font_family') or not pdf_obj.font_family:  # Check if font is set
                output_words.append(word)
                continue
            word_width = pdf_obj.get_string_width(word)  # This needs the font to be set
        except Exception:  # Catch if font not set or other FPDF errors
            output_words.append(word)
            continue

        if word_width > max_width and max_width > 0:  # max_width is effective cell width
            current_part = ""
            broken_word_parts = []
            for char_index, char_val in enumerate(word):
                if not hasattr(pdf_obj, 'font_family') or not pdf_obj.font_family:
                    current_part += char_val
                    continue
                current_part_width = pdf_obj.get_string_width(current_part + char_val)

                if current_part_width <= max_width:
                    current_part += char_val
                else:
                    if not current_part and char_index == 0:
                        broken_word_parts.append(char_val)
                        current_part = ""
                    else:
                        broken_word_parts.append(current_part + soft_hyphen)
                        current_part = char_val
            if current_part:
                broken_word_parts.append(current_part)
            output_words.extend(broken_word_parts)
        else:
            output_words.append(word)
    return ' '.join(output_words)

def safe_multicell(pdf_obj: 'FPDF', text: str, cell_height: float, font_name: str,
                   start_font_size: int = 8, min_font_size: int = 4, align: str = 'R',
                   border: int = 0, style: str = ''):
    """
    Safely renders text in a multi_cell in FPDF, adjusting font size if needed.
    رندر امن متن در multi_cell در FPDF، با تنظیم اندازه فونت در صورت نیاز.
    """
    if not config.LIBRARIES_LOADED_SUCCESSFULLY or not FPDF or not arabic_reshaper or not get_display or not hasattr(pdf_obj, 'set_font'):
        logger.error("FPDF, arabic_reshaper, bidi or set_font not available in safe_multicell.")
        return False

    current_font_size = start_font_size
    original_y = pdf_obj.get_y()
    original_x = pdf_obj.get_x()
    font_family_name = font_name

    while current_font_size >= min_font_size:
        pdf_obj.set_font(font_family_name, style=style, size=current_font_size)
        effective_width = pdf_obj.epw
        if effective_width <= 0:
            logger.error(f"  safe_multicell: Effective page width ({effective_width:.2f}) is not valid.")
            pdf_obj.ln(cell_height)
            return False

        reshaped_text = arabic_reshaper.reshape(text)
        bidi_text = get_display(reshaped_text)
        processed_text_for_fpdf = split_long_words(bidi_text, effective_width, pdf_obj)

        pdf_obj.set_xy(original_x, original_y)
        try:
            pdf_obj.multi_cell(w=effective_width, h=cell_height, text=processed_text_for_fpdf, align=align, border=border)
            return True
        except RuntimeError as e:
            if "Not enough horizontal space" in str(e) or "string wider than cell" in str(e).lower():
                current_font_size -= 1
                if current_font_size < min_font_size:
                    logger.error(f"  safe_multicell: Not enough space even at min font size {min_font_size} for text: '{text[:50]}...'")
                    pdf_obj.set_font(font_family_name, style=style, size=start_font_size)
                    pdf_obj.set_xy(original_x, original_y)
                    try:
                        pdf_obj.multi_cell(effective_width, cell_height, text=f"[Render Error: {text[:30]}...]", align=align, border=border)
                    except Exception:
                        pdf_obj.ln(cell_height)
                    return False
            else:
                logger.error(f"  safe_multicell: Unexpected RuntimeError: {e}")
                raise
        except Exception as e_other:
            logger.error(f"  safe_multicell: Unexpected Exception: {e_other}")
            raise
    pdf_obj.set_font(font_family_name, style=style, size=start_font_size)
    return False

def remove_emojis(text: str) -> str:
    """
    Removes emojis from a text string.
    حذف ایموجی‌ها از یک رشته متنی.
    """
    if not text: return ""
    emoji_pattern = re.compile(
        "[" "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F700-\U0001F77F"  # alchemical symbols
        "\U0001F780-\U0001F7FF"  # Geometric Shapes Extended
        "\U0001F800-\U0001F8FF"  # Supplemental Arrows-C
        "\U0001F900-\U0001F9FF"  # Supplemental Symbols and Pictographs
        "\U0001FA00-\U0001FA6F"  # Chess Symbols
        "\U0001FA70-\U0001FAFF"  # Symbols and Pictographs Extended-A
        "\U00002702-\U000027B0"  # Dingbats
        "\U000024C2-\U0001F251"
        "\u2300-\u23ff"          # Miscellaneous Technical
        "\u2600-\u26ff"          # Miscellaneous Symbols
        "\u2B00-\u2BFF"          # Miscellaneous Symbols and Arrows
        "]+", flags=re.UNICODE)
    return emoji_pattern.sub(r'', text)

def sanitize_text_for_telegram(text: str) -> str:
    """
    Sanitizes text for sending via Telegram, removing control characters and normalizing spaces.
    پاکسازی متن برای ارسال از طریق تلگرام، حذف کاراکترهای کنترلی و نرمال‌سازی فاصله‌ها.
    """
    if not text: return ""
    allowed_control_chars = {'\n', '\r', '\t'}
    control_chars_to_remove_list = [chr(i) for i in range(32) if chr(i) not in allowed_control_chars]
    control_chars_to_remove_list.append(chr(127)) # DEL character

    remove_map = str.maketrans('', '', "".join(control_chars_to_remove_list))
    sanitized_text = text.translate(remove_map)

    sanitized_text = re.sub(r'[ \t]+', ' ', sanitized_text)
    sanitized_text = re.sub(r'\n{3,}', '\n\n', sanitized_text)
    sanitized_text = "\n".join([line.strip() for line in sanitized_text.splitlines()])
    return sanitized_text.strip()

def encode_image_to_base64(image_path: str) -> str:
    """
    Encodes an image file to a base64 string.
    انکود کردن یک فایل تصویر به رشته base64.
    """
    logger.debug(f"انکود تصویر: {image_path}")
    try:
        return base64.b64encode(pathlib.Path(image_path).read_bytes()).decode('utf-8')
    except FileNotFoundError:
        logger.error(f"فایل تصویر برای انکود یافت نشد: {image_path}")
        return ""
    except Exception as e:
        logger.error(f"خطا در انکود تصویر {image_path}: {e}")
        return ""

def extract_content_parts_v2(ai_response_text: str) -> Tuple[str | None, str | None, str | None, str | None]:
    """
    Extracts different content parts from the AI's response based on predefined markers.
    استخراج بخش‌های مختلف محتوا از پاسخ AI بر اساس نشانگرهای از پیش تعریف شده.
    """
    logger.info("شروع استخراج بخش‌های پاسخ AI (نسخه ۲)")
    if not ai_response_text or not ai_response_text.strip():
        logger.warning("پاسخ AI برای استخراج خالی است.")
        return None, None, None, None

    decision_tag_match = re.search(r"(\[POST_THIS_ANALYSIS=(YES|NO)\])\s*$", ai_response_text, re.IGNORECASE | re.MULTILINE)
    decision_tag_content = None
    text_before_decision_tag = ai_response_text

    if decision_tag_match:
        decision_tag_content = decision_tag_match.group(1).strip().upper()
        text_before_decision_tag = ai_response_text[:decision_tag_match.start()].strip()
        logger.info(f"تگ تصمیم AI یافت شد: {decision_tag_content}")
    else:
        logger.warning("تگ تصمیم [POST_THIS_ANALYSIS=...] در انتهای پاسخ AI یافت نشد.")

    full_analysis_for_files_content = text_before_decision_tag
    public_content_marker = "--- پایان بخش‌های عمومی برای انتشار در تلگرام ---"
    marker_position = text_before_decision_tag.find(public_content_marker)
    core_analysis_for_public = None
    admin_recommendation_section = None

    if marker_position != -1:
        core_analysis_for_public = text_before_decision_tag[:marker_position].strip()
        admin_candidate_text = text_before_decision_tag[marker_position + len(public_content_marker):].strip()
        if admin_candidate_text.startswith("بخش ۶:") or admin_candidate_text.startswith("بخش 6:"):
            admin_recommendation_section = admin_candidate_text
            logger.info("بخش عمومی و بخش توصیه ادمین با موفقیت جدا شدند.")
        elif admin_candidate_text:
            logger.warning(f"محتوا پس از مارکر '{public_content_marker}' یافت شد اما با 'بخش ۶:' شروع نمی‌شود. این بخش به عنوان توصیه ادمین در نظر گرفته شد.")
            admin_recommendation_section = admin_candidate_text
        else:
            logger.info(f"مارکر '{public_content_marker}' یافت شد اما محتوایی پس از آن برای بخش ادمین وجود ندارد.")
    else:
        logger.warning(f"مارکر '{public_content_marker}' برای جداسازی بخش عمومی و ادمین یافت نشد.")
        if text_before_decision_tag.strip().startswith("بخش ۶:") or text_before_decision_tag.strip().startswith("بخش 6:"):
            logger.warning("کل متن قبل از تگ تصمیم، شبیه به بخش ادمین است (شروع با 'بخش ۶:'). بخش عمومی خالی در نظر گرفته شد.")
            admin_recommendation_section = text_before_decision_tag
            core_analysis_for_public = ""
        else:
            core_analysis_for_public = text_before_decision_tag

    if not full_analysis_for_files_content and ai_response_text:
        full_analysis_for_files_content = ai_response_text.strip()

    return decision_tag_content, core_analysis_for_public, admin_recommendation_section, full_analysis_for_files_content

logger.info("utils.py loaded.")
