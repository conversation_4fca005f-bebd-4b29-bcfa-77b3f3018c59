# telegram_sender.py
"""
ماژول مربوط به ارسال پیام و فایل به تلگرام (به‌روز شده برای MarkdownV2)
Module for sending messages and files to Telegram (Updated for MarkdownV2).
"""
import asyncio
import os
import pathlib # For ensuring path operations are robust
import logging
from typing import List, Optional # For type hinting

try:
    import telegram
    from telegram.constants import ParseMode
    from telegram.error import TelegramError
    from telegram.ext import ApplicationBuilder
    # For robust Markdown escaping with python-telegram-bot v20+
    # from telegram.helpers import escape_markdown # If you use this, uncomment and ensure library version.
except ImportError:
    telegram = None
    ParseMode = None # type: ignore
    TelegramError = Exception # Fallback
    ApplicationBuilder = None
    # escape_markdown = None
    logger_telegram = logging.getLogger(__name__) # Define logger here if telegram fails to import
    logger_telegram.warning("python-telegram-bot library not found. Telegram sending will be disabled.")


from utils import sanitize_text_for_telegram # Assuming utils.py contains this

logger = logging.getLogger(__name__)

async def send_to_telegram(
    text: str,
    chart_paths: Optional[List[str]] = None,
    bot_token: Optional[str] = None,
    chat_id: Optional[str] = None,
    preferred_parse_mode: str = ParseMode.MARKDOWN_V2 if ParseMode else "MarkdownV2" # Default to MarkdownV2
):
    """
    Sends text and/or charts to a Telegram chat.
    Defaults to MarkdownV2 for text. Falls back to plain text on error.
    """
    # Import config here to ensure environment variables are loaded first
    import config

    if chart_paths is None:
        chart_paths = []

    # Use token/chat_id from parameters if provided, else from config
    effective_bot_token = bot_token if bot_token else config.TELEGRAM_BOT_TOKEN
    effective_chat_id = chat_id if chat_id else config.TELEGRAM_CHAT_ID

    logger.info(f"Attempting to send to Telegram: chat_id={effective_chat_id}, ParseMode={preferred_parse_mode}")

    if not all([telegram, ApplicationBuilder, ParseMode]) or \
       not effective_bot_token or not effective_chat_id:
        logger.warning("Telegram sending prerequisites not met (config, library missing, or token/chat_id missing). Message not sent.")
        return

    try:
        # Consider creating the Application/Bot instance outside if sending many messages in sequence,
        # but for a single report, creating it here is fine.
        app = ApplicationBuilder().token(effective_bot_token).connect_timeout(30).read_timeout(60).build()
        bot = app.bot

        if text:
            # The text is assumed to be pre-formatted with MarkdownV2 by format_setup_status_report.
            # If 'text' itself might contain user input with special Markdown characters that are NOT
            # part of intentional formatting, those specific parts should be escaped *before* being
            # passed into format_setup_status_report or into this function.
            # Example (if you were to use library's escape function on a raw string):
            # if preferred_parse_mode == ParseMode.MARKDOWN_V2 and escape_markdown:
            #     text_to_send = escape_markdown(text, version=2)
            # else:
            #     text_to_send = text
            text_to_send = sanitize_text_for_telegram(text) # General sanitization

            max_chunk_size = 4000 # Telegram's limit is 4096 bytes
            chunks = []
            current_chunk = ""
            for line in text_to_send.split('\n'):
                if len(current_chunk) + len(line) + 1 > max_chunk_size:
                    chunks.append(current_chunk)
                    current_chunk = line
                else:
                    current_chunk = (current_chunk + "\n" + line) if current_chunk else line
            if current_chunk:
                chunks.append(current_chunk)

            for i, chunk in enumerate(chunks):
                try:
                    await bot.send_message(effective_chat_id, chunk, parse_mode=preferred_parse_mode)
                    logger.info(f"Text chunk {i+1}/{len(chunks)} sent to Telegram using {preferred_parse_mode}.")
                except TelegramError as e_fmt: # Catch errors related to parsing or sending
                    logger.warning(f"Error sending chunk {i+1} with {preferred_parse_mode}: {e_fmt}. Attempting plain text.")
                    try:
                        await bot.send_message(effective_chat_id, chunk) # Send raw chunk
                        logger.info(f"Text chunk {i+1}/{len(chunks)} sent successfully as plain text after {preferred_parse_mode} failed.")
                    except Exception as e_plain:
                        logger.error(f"Final error sending chunk {i+1} as plain text: {e_plain}")
                except Exception as e_generic:
                     logger.error(f"Generic error sending chunk {i+1} to Telegram: {e_generic}")
                if i < len(chunks) - 1:
                    await asyncio.sleep(0.75) # Small delay between message chunks

        if chart_paths:
            media_group_inputs = []  # List[telegram.InputMediaPhoto]
            existing_chart_paths = [p for p in chart_paths if os.path.exists(p)]

            for chart_path_str in existing_chart_paths:
                try:
                    # python-telegram-bot v20+ uses InputFile which handles opening/closing
                    # For older versions, manual file handling was needed.
                    # Assuming InputFile handles the file path directly for InputMediaPhoto
                    media_group_inputs.append(telegram.InputMediaPhoto(media=pathlib.Path(chart_path_str)))
                except Exception as e_tg_photo_prep:
                    logger.error(f"Error preparing Telegram photo {chart_path_str}: {e_tg_photo_prep}")
                    continue

            # Send photos in groups of 10 (Telegram limit for media group)
            for i in range(0, len(media_group_inputs), 10):
                media_group_to_send = media_group_inputs[i:i + 10]
                if media_group_to_send:
                    try:
                        await bot.send_media_group(effective_chat_id, media_group_to_send)
                        logger.info(f"Photo media group {i//10 + 1} sent to Telegram ({len(media_group_to_send)} images).")
                        await asyncio.sleep(config.REQUEST_DELAY_SECONDS) # Delay between sending groups
                    except Exception as e_tg_group_send:
                        logger.error(f"Error sending photo media group to Telegram: {e_tg_group_send}")

    except Exception as e:
        logger.error(f"Overall error in send_to_telegram function: {e}", exc_info=True)

# This logger statement was at the end of the original file from user upload.
logger.info("telegram_sender.py (updated for MarkdownV2 default) loaded.")