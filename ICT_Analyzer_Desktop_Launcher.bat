@echo off
title ICT Analyzer GUI - Desktop Launcher
echo.
echo ========================================
echo    ICT Analyzer GUI - Desktop Launcher
echo ========================================
echo.

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Navigate to the ICT Analyzer Portable directory
cd /d "%SCRIPT_DIR%ICT_Analyzer_Portable"

REM Check if the executable exists
if not exist "ICT_Analyzer_GUI.exe" (
    echo ERROR: ICT_Analyzer_GUI.exe not found!
    echo.
    echo Please ensure this launcher is in the same directory as ICT_Analyzer_Portable folder.
    echo.
    echo Expected structure:
    echo   Your_Directory\
    echo   ├── ICT_Analyzer_Desktop_Launcher.bat  ^(this file^)
    echo   └── ICT_Analyzer_Portable\
    echo       └── ICT_Analyzer_GUI.exe
    echo.
    pause
    exit /b 1
)

echo Starting ICT Analyzer GUI...
echo Location: %CD%
echo.

REM Launch the ICT Analyzer GUI
start "" "ICT_Analyzer_GUI.exe"

REM Wait a moment to see if it starts successfully
timeout /t 2 /nobreak >nul

echo ICT Analyzer GUI launched successfully!
echo You can close this window now.
echo.
pause
