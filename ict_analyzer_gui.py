#!/usr/bin/env python3
"""
ICT Analyzer GUI Application
A comprehensive Windows desktop application for ICT trading analysis
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext, simpledialog
import threading
import queue
import json
import os
import sys
from datetime import datetime, timezone
import asyncio
from typing import Optional, Dict, Any, List
import logging
import hashlib
import secrets

# Add the current directory to Python path for imports
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    import config
    from chart_agent_module import ChartAgent
    from setup_tracker import get_all_open_setups, track_specific_open_setup
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are in the same directory as this GUI application.")
    sys.exit(1)

class ICTAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("ICT Analyzer - Professional Trading Analysis")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Configure style
        self.setup_styles()

        # Initialize variables
        self.chart_agent = None
        self.analysis_thread = None
        self.message_queue = queue.Queue()
        self.is_analyzing = False
        self.user_preferences = self.load_preferences()

        # Setup logging
        self.setup_logging()

        # Create GUI components
        self.create_menu()
        self.create_main_interface()
        self.create_status_bar()

        # Start message processing
        self.process_messages()

        # Load saved preferences
        self.load_user_settings()

        # Initialize chart agent
        self.initialize_chart_agent()

    def setup_styles(self):
        """Configure the application's visual style"""
        style = ttk.Style()

        # Configure colors and fonts
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72',
            'success': '#F18F01',
            'danger': '#C73E1D',
            'background': '#F5F5F5',
            'surface': '#FFFFFF',
            'text': '#333333',
            'text_light': '#666666'
        }

        # Configure ttk styles
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground=self.colors['primary'])
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'), foreground=self.colors['text'])
        style.configure('Status.TLabel', font=('Arial', 10), foreground=self.colors['text_light'])
        style.configure('Success.TLabel', font=('Arial', 10, 'bold'), foreground=self.colors['success'])
        style.configure('Danger.TLabel', font=('Arial', 10, 'bold'), foreground=self.colors['danger'])

        # Configure button styles
        style.configure('Primary.TButton', font=('Arial', 10, 'bold'))
        style.configure('Secondary.TButton', font=('Arial', 9))

    def setup_logging(self):
        """Setup logging for the GUI application"""
        log_dir = os.path.join(current_dir, 'logs')
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, f'ict_gui_{datetime.now().strftime("%Y%m%d")}.log')

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )

        self.logger = logging.getLogger('ICTAnalyzerGUI')
        self.logger.info("ICT Analyzer GUI started")

    def create_menu(self):
        """Create the application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Import Settings", command=self.import_settings)
        file_menu.add_command(label="Export Settings", command=self.export_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)

        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Settings", command=self.open_settings)
        tools_menu.add_command(label="View Logs", command=self.view_logs)
        tools_menu.add_command(label="Clear Cache", command=self.clear_cache)

        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
        help_menu.add_command(label="User Guide", command=self.show_user_guide)

    def create_main_interface(self):
        """Create the main interface layout"""
        # Create main container with padding
        main_container = ttk.Frame(self.root, padding="10")
        main_container.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_container.columnconfigure(1, weight=1)
        main_container.rowconfigure(1, weight=1)

        # Create left panel for controls
        self.create_control_panel(main_container)

        # Create right panel for results and status
        self.create_results_panel(main_container)

    def create_control_panel(self, parent):
        """Create the left control panel"""
        control_frame = ttk.LabelFrame(parent, text="Analysis Controls", padding="10")
        control_frame.grid(row=0, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        control_frame.columnconfigure(0, weight=1)

        # Title
        title_label = ttk.Label(control_frame, text="ICT Analyzer", style='Title.TLabel')
        title_label.grid(row=0, column=0, pady=(0, 20))

        # Symbol input section
        self.create_symbol_input(control_frame)

        # Action buttons section
        self.create_action_buttons(control_frame)

        # Quick settings section
        self.create_quick_settings(control_frame)

    def create_symbol_input(self, parent):
        """Create symbol input controls"""
        symbol_frame = ttk.LabelFrame(parent, text="Symbol Selection", padding="10")
        symbol_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        symbol_frame.columnconfigure(1, weight=1)

        # Base currency
        ttk.Label(symbol_frame, text="Base Currency:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.base_currency_var = tk.StringVar(value="BTC")
        base_combo = ttk.Combobox(symbol_frame, textvariable=self.base_currency_var, width=15)
        base_combo['values'] = ('BTC', 'ETH', 'ADA', 'SOL', 'MATIC', 'DOT', 'LINK', 'UNI', 'AAVE', 'SUSHI',
                                'XRP', 'AVAX', 'ATOM', 'FTM', 'NEAR', 'ALGO', 'VET', 'ICP', 'THETA', 'FIL',
                                'TRX', 'ETC', 'XLM', 'MANA', 'SAND', 'CRV', 'GRT', 'ENJ', 'BAT', 'ZEC')
        base_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # Quote currency
        ttk.Label(symbol_frame, text="Quote Currency:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.quote_currency_var = tk.StringVar(value="USD")
        quote_combo = ttk.Combobox(symbol_frame, textvariable=self.quote_currency_var, width=15)
        quote_combo['values'] = ('USD', 'USDT', 'USDC', 'EUR', 'GBP', 'BTC', 'ETH', 'BNB', 'JPY', 'CAD', 'AUD')
        quote_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # Recent symbols
        ttk.Label(symbol_frame, text="Recent:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.recent_symbols_var = tk.StringVar()
        recent_combo = ttk.Combobox(symbol_frame, textvariable=self.recent_symbols_var, width=15)
        recent_combo['values'] = self.user_preferences.get('recent_symbols', [])
        recent_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)
        recent_combo.bind('<<ComboboxSelected>>', self.on_recent_symbol_selected)

    def create_action_buttons(self, parent):
        """Create main action buttons"""
        button_frame = ttk.LabelFrame(parent, text="Actions", padding="10")
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        button_frame.columnconfigure(0, weight=1)

        # Analyze Symbol button
        self.analyze_btn = ttk.Button(
            button_frame,
            text="🔍 Analyze Symbol",
            command=self.start_analysis,
            style='Primary.TButton'
        )
        self.analyze_btn.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=2)

        # Auto Scan button
        self.auto_scan_btn = ttk.Button(
            button_frame,
            text="🔄 Auto Scan",
            command=self.start_auto_scan,
            style='Secondary.TButton'
        )
        self.auto_scan_btn.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)

        # Track All Setups button
        self.track_all_btn = ttk.Button(
            button_frame,
            text="📊 Track All Setups",
            command=self.track_all_setups,
            style='Secondary.TButton'
        )
        self.track_all_btn.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=2)

        # Track Specific Setup button
        self.track_specific_btn = ttk.Button(
            button_frame,
            text="🎯 Track Specific Setup",
            command=self.track_specific_setup,
            style='Secondary.TButton'
        )
        self.track_specific_btn.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=2)

    def create_quick_settings(self, parent):
        """Create quick settings panel"""
        settings_frame = ttk.LabelFrame(parent, text="Quick Settings", padding="10")
        settings_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # AI Threshold
        ttk.Label(settings_frame, text="AI Threshold:").grid(row=0, column=0, sticky=tk.W)
        self.ai_threshold_var = tk.DoubleVar(value=22.5)
        threshold_scale = ttk.Scale(
            settings_frame,
            from_=10.0,
            to=50.0,
            variable=self.ai_threshold_var,
            orient=tk.HORIZONTAL
        )
        threshold_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))

        self.threshold_label = ttk.Label(settings_frame, text="22.5")
        self.threshold_label.grid(row=0, column=2, padx=(5, 0))
        threshold_scale.configure(command=self.update_threshold_label)

        # Auto-send to Telegram
        self.auto_telegram_var = tk.BooleanVar(value=True)
        telegram_check = ttk.Checkbutton(
            settings_frame,
            text="Auto-send to Telegram",
            variable=self.auto_telegram_var
        )
        telegram_check.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))

    def create_results_panel(self, parent):
        """Create the results and status panel"""
        # Create notebook for tabbed interface
        self.notebook = ttk.Notebook(parent)
        self.notebook.grid(row=0, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Analysis Results tab
        self.create_analysis_tab()

        # Setup Tracking tab
        self.create_tracking_tab()

        # Logs tab
        self.create_logs_tab()

    def create_analysis_tab(self):
        """Create the analysis results tab"""
        analysis_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(analysis_frame, text="Analysis Results")
        analysis_frame.columnconfigure(0, weight=1)
        analysis_frame.rowconfigure(1, weight=1)

        # Status section
        status_frame = ttk.LabelFrame(analysis_frame, text="Current Status", padding="10")
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)

        ttk.Label(status_frame, text="Status:").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text="Ready", style='Status.TLabel')
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame,
            variable=self.progress_var,
            mode='determinate'
        )
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 0))

        # Results section
        results_frame = ttk.LabelFrame(analysis_frame, text="Analysis Results", padding="10")
        results_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)

        # Results text area with scrollbar
        self.results_text = scrolledtext.ScrolledText(
            results_frame,
            wrap=tk.WORD,
            font=('Consolas', 10),
            state=tk.DISABLED
        )
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def create_tracking_tab(self):
        """Create the setup tracking tab"""
        tracking_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(tracking_frame, text="Setup Tracking")
        tracking_frame.columnconfigure(0, weight=1)
        tracking_frame.rowconfigure(1, weight=1)

        # Controls
        controls_frame = ttk.Frame(tracking_frame)
        controls_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(
            controls_frame,
            text="🔄 Refresh",
            command=self.refresh_setups
        ).grid(row=0, column=0, padx=(0, 10))

        ttk.Button(
            controls_frame,
            text="📊 Export Report",
            command=self.export_setups_report
        ).grid(row=0, column=1)

        # Setups tree view
        self.create_setups_treeview(tracking_frame)

    def create_setups_treeview(self, parent):
        """Create the setups treeview"""
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Define columns
        columns = ('Symbol', 'Direction', 'Status', 'Entry', 'Current', 'PnL', 'Created')

        self.setups_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)

        # Configure column headings and widths
        column_widths = {'Symbol': 80, 'Direction': 80, 'Status': 100, 'Entry': 100, 'Current': 100, 'PnL': 80, 'Created': 120}

        for col in columns:
            self.setups_tree.heading(col, text=col)
            self.setups_tree.column(col, width=column_widths.get(col, 100), minwidth=50)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.setups_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.setups_tree.xview)
        self.setups_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Grid layout
        self.setups_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Bind double-click event
        self.setups_tree.bind('<Double-1>', self.on_setup_double_click)

    def create_logs_tab(self):
        """Create the logs tab"""
        logs_frame = ttk.Frame(self.notebook, padding="10")
        self.notebook.add(logs_frame, text="Logs")
        logs_frame.columnconfigure(0, weight=1)
        logs_frame.rowconfigure(1, weight=1)

        # Controls
        logs_controls = ttk.Frame(logs_frame)
        logs_controls.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Button(
            logs_controls,
            text="🔄 Refresh",
            command=self.refresh_logs
        ).grid(row=0, column=0, padx=(0, 10))

        ttk.Button(
            logs_controls,
            text="🗑️ Clear",
            command=self.clear_logs
        ).grid(row=0, column=1)

        # Logs text area
        self.logs_text = scrolledtext.ScrolledText(
            logs_frame,
            wrap=tk.WORD,
            font=('Consolas', 9),
            state=tk.DISABLED
        )
        self.logs_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def create_status_bar(self):
        """Create the status bar at the bottom"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.status_bar.columnconfigure(1, weight=1)

        # Connection status
        self.connection_label = ttk.Label(self.status_bar, text="●", foreground='red')
        self.connection_label.grid(row=0, column=0, padx=(10, 5))

        # Status text
        self.status_text = ttk.Label(self.status_bar, text="Ready")
        self.status_text.grid(row=0, column=1, sticky=tk.W)

        # Time
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.grid(row=0, column=2, padx=(0, 10))

        # Update time every second
        self.update_time()

    # Event handlers and utility methods

    def initialize_chart_agent(self):
        """Initialize the chart agent"""
        try:
            self.chart_agent = ChartAgent()
            self.update_status("Chart Agent initialized successfully", 'success')
            self.connection_label.configure(foreground='green')
        except Exception as e:
            self.logger.error(f"Failed to initialize chart agent: {e}")
            self.update_status(f"Failed to initialize: {e}", 'error')
            messagebox.showerror("Initialization Error", f"Failed to initialize chart agent:\n{e}")

    def update_status(self, message: str, status_type: str = 'info'):
        """Update the status display"""
        self.status_label.configure(text=message)
        self.status_text.configure(text=message)

        if status_type == 'success':
            self.status_label.configure(style='Success.TLabel')
        elif status_type == 'error':
            self.status_label.configure(style='Danger.TLabel')
        else:
            self.status_label.configure(style='Status.TLabel')

        self.logger.info(f"Status: {message}")

    def update_progress(self, value: float, message: str = ""):
        """Update the progress bar"""
        self.progress_var.set(value)
        if message:
            self.update_status(message)

    def update_threshold_label(self, value):
        """Update the threshold label"""
        self.threshold_label.configure(text=f"{float(value):.1f}")

    def update_time(self):
        """Update the time display"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.configure(text=current_time)
        self.root.after(1000, self.update_time)

    def on_recent_symbol_selected(self, event):
        """Handle recent symbol selection"""
        selected = self.recent_symbols_var.get()
        if selected and '-' in selected:
            base, quote = selected.split('-', 1)
            self.base_currency_var.set(base)
            self.quote_currency_var.set(quote)

    def add_to_recent_symbols(self, symbol: str):
        """Add symbol to recent symbols list"""
        recent = self.user_preferences.get('recent_symbols', [])
        if symbol in recent:
            recent.remove(symbol)
        recent.insert(0, symbol)
        recent = recent[:10]  # Keep only last 10
        self.user_preferences['recent_symbols'] = recent
        self.save_preferences()

    def start_analysis(self):
        """Start symbol analysis in a separate thread"""
        if self.is_analyzing:
            messagebox.showwarning("Analysis in Progress", "An analysis is already running. Please wait for it to complete.")
            return

        # Check API key configuration first
        if not config.CRYPTOCOMPARE_API_KEY or config.CRYPTOCOMPARE_API_KEY == "your_cryptocompare_api_key_here":
            messagebox.showerror(
                "API Key Required",
                "Please configure your CryptoCompare API key before running analysis.\n\n"
                "1. Go to the Settings tab\n"
                "2. Enter your CryptoCompare API key\n"
                "3. Click 'Save Configuration'\n"
                "4. Return to Analysis tab and try again\n\n"
                "You can get a free API key from: https://cryptocompare.com"
            )
            return

        base = self.base_currency_var.get().strip().upper()
        quote = self.quote_currency_var.get().strip().upper()

        if not base or not quote:
            messagebox.showerror("Input Error", "Please enter both base and quote currencies.")
            return

        symbol = f"{base}-{quote}"
        self.add_to_recent_symbols(symbol)

        # Disable buttons
        self.set_buttons_state(False)
        self.is_analyzing = True

        # Clear previous results
        self.clear_results()

        # Start analysis thread
        self.analysis_thread = threading.Thread(
            target=self.run_analysis_thread,
            args=(base, quote),
            daemon=True
        )
        self.analysis_thread.start()

    def run_analysis_thread(self, base: str, quote: str):
        """Run analysis in a separate thread"""
        try:
            self.message_queue.put(('status', 'Starting analysis...', 'info'))
            self.message_queue.put(('progress', 10, 'Initializing...'))

            # Check API key configuration
            if not config.CRYPTOCOMPARE_API_KEY or config.CRYPTOCOMPARE_API_KEY == "your_cryptocompare_api_key_here":
                raise Exception("CryptoCompare API key not configured. Please set your API key in the Settings tab.")

            if not self.chart_agent:
                self.initialize_chart_agent()
                if not self.chart_agent:
                    raise Exception("Chart agent not available")

            # Set currency pair
            self.chart_agent.set_currency_pair(base, quote)
            self.message_queue.put(('progress', 20, f'Analyzing {base}-{quote}...'))

            # Run analysis
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                loop.run_until_complete(self.chart_agent.run('C', False))
                self.message_queue.put(('progress', 90, 'Analysis complete, formatting results...'))

                # Get results
                results = self.format_analysis_results()
                self.message_queue.put(('results', results))
                self.message_queue.put(('progress', 100, 'Analysis completed successfully'))

            finally:
                loop.close()

        except Exception as e:
            self.logger.error(f"Analysis error: {e}")
            self.message_queue.put(('error', f"Analysis failed: {e}"))
        finally:
            self.message_queue.put(('complete', None))

    def format_analysis_results(self) -> str:
        """Format the analysis results for display"""
        if not self.chart_agent:
            return "No analysis results available."

        results = []
        results.append("=" * 60)
        results.append(f"ICT ANALYSIS RESULTS - {config.TICKER_DISPLAY_NAME_GLOBAL}")
        results.append("=" * 60)
        results.append("")

        # AI Evaluation Results
        if hasattr(self.chart_agent, '_last_evaluation_score'):
            results.append("🤖 AI EVALUATION SUMMARY")
            results.append("-" * 30)
            results.append(f"Score: {self.chart_agent._last_evaluation_score:.2f}")
            results.append(f"Threshold: {self.chart_agent.ai_send_threshold:.2f}")
            results.append(f"Decision: {'✅ SEND TO AI' if self.chart_agent._last_evaluation_score >= self.chart_agent.ai_send_threshold else '❌ DO NOT SEND'}")
            results.append("")
            results.append("Reasoning:")
            results.append(self.chart_agent._last_evaluation_reasoning or "No reasoning available")
            results.append("")

        # Analysis Summary by Timeframe
        if self.chart_agent.all_timeframes_ict_analysis:
            results.append("📊 TIMEFRAME ANALYSIS SUMMARY")
            results.append("-" * 30)

            for tf, data in self.chart_agent.all_timeframes_ict_analysis.items():
                if isinstance(data, dict) and 'error' not in data:
                    results.append(f"\n{tf} Timeframe:")

                    # Trend analysis
                    trend = data.get('market_trend_analysis', {})
                    if trend:
                        consensus = trend.get('overall_trend_consensus', 'N/A')
                        strength = trend.get('trend_strength_adx_category', 'N/A')
                        results.append(f"  Trend: {consensus} ({strength})")

                    # Structure events
                    bos_choch = data.get('bos_choch_events', [])
                    if bos_choch:
                        high_quality = [e for e in bos_choch if isinstance(e, dict) and e.get('confirmation_quality_score', 0) >= 15]
                        results.append(f"  Structure Events: {len(bos_choch)} total, {len(high_quality)} high quality")

                    # FVGs
                    fvgs = data.get('fair_value_gaps', [])
                    if fvgs:
                        unmitigated = [f for f in fvgs if isinstance(f, dict) and f.get('mitigation_info', {}).get('status') == 'unmitigated']
                        results.append(f"  FVGs: {len(fvgs)} total, {len(unmitigated)} unmitigated")

                    # OTE zones
                    otes = data.get('optimal_trade_entry_zones', [])
                    if otes:
                        results.append(f"  OTE Zones: {len(otes)}")

        # AI Response Summary
        if self.chart_agent.decision_to_post_tag:
            results.append("\n🎯 AI DECISION")
            results.append("-" * 30)
            results.append(f"Decision Tag: {self.chart_agent.decision_to_post_tag}")

            if self.chart_agent.core_analysis_from_ai:
                results.append("\nCore Analysis:")
                # Truncate for display
                core_analysis = self.chart_agent.core_analysis_from_ai[:500]
                if len(self.chart_agent.core_analysis_from_ai) > 500:
                    core_analysis += "... (truncated)"
                results.append(core_analysis)

        results.append("\n" + "=" * 60)
        results.append(f"Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return "\n".join(results)

    def start_auto_scan(self):
        """Start auto scan functionality"""
        if self.is_analyzing:
            messagebox.showwarning("Analysis in Progress", "Please wait for current analysis to complete.")
            return

        # This would implement the auto-scan functionality
        messagebox.showinfo("Auto Scan", "Auto scan functionality will be implemented in the next version.")

    def track_all_setups(self):
        """Track all open setups"""
        try:
            self.update_status("Loading all open setups...")
            setups = get_all_open_setups()
            self.populate_setups_tree(setups)
            self.notebook.select(1)  # Switch to tracking tab
            self.update_status(f"Loaded {len(setups)} open setups")
        except Exception as e:
            self.logger.error(f"Error loading setups: {e}")
            messagebox.showerror("Error", f"Failed to load setups:\n{e}")

    def track_specific_setup(self):
        """Track a specific setup"""
        symbol = tk.simpledialog.askstring("Track Setup", "Enter symbol (e.g., BTC-USD):")
        if symbol:
            try:
                # This would implement specific setup tracking
                messagebox.showinfo("Track Setup", f"Tracking setup for {symbol}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to track setup:\n{e}")

    def populate_setups_tree(self, setups: List[Dict]):
        """Populate the setups treeview with data"""
        # Clear existing items
        for item in self.setups_tree.get_children():
            self.setups_tree.delete(item)

        # Add setups
        for setup in setups:
            values = (
                setup.get('ticker_display_name', 'N/A'),
                setup.get('direction', 'N/A'),
                setup.get('status', 'N/A'),
                f"{setup.get('entry_price_target_min', 0):.4f}" if setup.get('entry_price_target_min') else 'N/A',
                'N/A',  # Current price would need to be fetched
                'N/A',  # PnL would need to be calculated
                setup.get('creation_timestamp_utc', 'N/A')[:16] if setup.get('creation_timestamp_utc') else 'N/A'
            )
            self.setups_tree.insert('', tk.END, values=values)

    def on_setup_double_click(self, event):
        """Handle double-click on setup item"""
        selection = self.setups_tree.selection()
        if selection:
            item = self.setups_tree.item(selection[0])
            symbol = item['values'][0]
            messagebox.showinfo("Setup Details", f"Setup details for {symbol}")

    def refresh_setups(self):
        """Refresh the setups display"""
        self.track_all_setups()

    def refresh_logs(self):
        """Refresh the logs display"""
        try:
            log_file = os.path.join(current_dir, 'logs', f'ict_gui_{datetime.now().strftime("%Y%m%d")}.log')
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    logs = f.read()

                self.logs_text.configure(state=tk.NORMAL)
                self.logs_text.delete(1.0, tk.END)
                self.logs_text.insert(tk.END, logs)
                self.logs_text.configure(state=tk.DISABLED)
                self.logs_text.see(tk.END)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load logs:\n{e}")

    def clear_logs(self):
        """Clear the logs display"""
        self.logs_text.configure(state=tk.NORMAL)
        self.logs_text.delete(1.0, tk.END)
        self.logs_text.configure(state=tk.DISABLED)

    def clear_results(self):
        """Clear the results display"""
        self.results_text.configure(state=tk.NORMAL)
        self.results_text.delete(1.0, tk.END)
        self.results_text.configure(state=tk.DISABLED)
        self.progress_var.set(0)

    def set_buttons_state(self, enabled: bool):
        """Enable or disable action buttons"""
        state = tk.NORMAL if enabled else tk.DISABLED
        self.analyze_btn.configure(state=state)
        self.auto_scan_btn.configure(state=state)
        self.track_all_btn.configure(state=state)
        self.track_specific_btn.configure(state=state)

    def process_messages(self):
        """Process messages from worker threads"""
        try:
            while True:
                message_type, *args = self.message_queue.get_nowait()

                if message_type == 'status':
                    message, status_type = args
                    self.update_status(message, status_type)

                elif message_type == 'progress':
                    value, message = args
                    self.update_progress(value, message)

                elif message_type == 'results':
                    results = args[0]
                    self.results_text.configure(state=tk.NORMAL)
                    self.results_text.delete(1.0, tk.END)
                    self.results_text.insert(tk.END, results)
                    self.results_text.configure(state=tk.DISABLED)

                elif message_type == 'error':
                    error_msg = args[0]
                    self.update_status(error_msg, 'error')
                    messagebox.showerror("Analysis Error", error_msg)

                elif message_type == 'complete':
                    self.is_analyzing = False
                    self.set_buttons_state(True)
                    self.update_progress(0)

        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.process_messages)

    def load_preferences(self) -> Dict:
        """Load user preferences from file"""
        prefs_file = os.path.join(current_dir, 'gui_preferences.json')
        try:
            if os.path.exists(prefs_file):
                with open(prefs_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load preferences: {e}")

        return {
            'recent_symbols': [],
            'window_geometry': '1200x800',
            'ai_threshold': 22.5,
            'auto_telegram': True
        }

    def save_preferences(self):
        """Save user preferences to file"""
        prefs_file = os.path.join(current_dir, 'gui_preferences.json')
        try:
            # Update current settings
            self.user_preferences['window_geometry'] = self.root.geometry()
            self.user_preferences['ai_threshold'] = self.ai_threshold_var.get()
            self.user_preferences['auto_telegram'] = self.auto_telegram_var.get()

            with open(prefs_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_preferences, f, indent=2)
        except Exception as e:
            self.logger.warning(f"Failed to save preferences: {e}")

    def load_user_settings(self):
        """Load user settings into the GUI"""
        # Set window geometry
        geometry = self.user_preferences.get('window_geometry', '1200x800')
        self.root.geometry(geometry)

        # Set AI threshold
        threshold = self.user_preferences.get('ai_threshold', 22.5)
        self.ai_threshold_var.set(threshold)
        self.threshold_label.configure(text=f"{threshold:.1f}")

        # Set auto telegram
        auto_telegram = self.user_preferences.get('auto_telegram', True)
        self.auto_telegram_var.set(auto_telegram)

    def import_settings(self):
        """Import settings from file"""
        file_path = filedialog.askopenfilename(
            title="Import Settings",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_prefs = json.load(f)

                self.user_preferences.update(imported_prefs)
                self.load_user_settings()
                self.save_preferences()

                messagebox.showinfo("Success", "Settings imported successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to import settings:\n{e}")

    def export_settings(self):
        """Export settings to file"""
        file_path = filedialog.asksaveasfilename(
            title="Export Settings",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self.user_preferences, f, indent=2)

                messagebox.showinfo("Success", "Settings exported successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export settings:\n{e}")

    def export_setups_report(self):
        """Export setups report to file"""
        file_path = filedialog.asksaveasfilename(
            title="Export Setups Report",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )

        if file_path:
            try:
                import csv
                setups = get_all_open_setups()

                with open(file_path, 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Symbol', 'Direction', 'Status', 'Entry Min', 'Entry Max', 'Stop Loss', 'Created'])

                    for setup in setups:
                        writer.writerow([
                            setup.get('ticker_display_name', ''),
                            setup.get('direction', ''),
                            setup.get('status', ''),
                            setup.get('entry_price_target_min', ''),
                            setup.get('entry_price_target_max', ''),
                            setup.get('stop_loss_price', ''),
                            setup.get('creation_timestamp_utc', '')
                        ])

                messagebox.showinfo("Success", f"Report exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to export report:\n{e}")

    def open_settings(self):
        """Open settings dialog with password protection"""
        # Check if password is set and validate
        try:
            if self.validate_settings_password():
                SettingsDialog(self.root, self.user_preferences, self.save_preferences)
        except Exception as e:
            # If password system fails, offer bypass option
            self.logger.error(f"Password system error: {e}")
            if messagebox.askyesno("Password System Error",
                f"Password system encountered an error: {e}\n\n"
                "Would you like to bypass password protection and open settings directly?\n"
                "(This is a temporary workaround - password protection will be restored next time)"):
                SettingsDialog(self.root, self.user_preferences, self.save_preferences)

    def validate_settings_password(self):
        """Validate settings password or prompt to set one"""
        password_hash = self.user_preferences.get('settings_password_hash', None)

        if password_hash is None:
            # First time - ask if user wants password protection
            if messagebox.askyesno("Settings Access",
                "🔒 Settings are password-protected for security.\n\n"
                "Choose your option:\n"
                "• YES: Use password protection (recommended)\n"
                "• NO: Skip password protection (troubleshooting only)\n\n"
                "Use password protection?"):
                # User wants password protection - set password
                return self.set_settings_password()
            else:
                # User chose to skip password protection
                messagebox.showinfo("Password Skipped",
                    "⚠️ Password protection skipped for this session.\n\n"
                    "Settings will be accessible without password.\n"
                    "Password protection will be restored next time.")
                return True
        else:
            # Password already exists - validate it directly
            return self.check_settings_password(password_hash)

    def set_settings_password(self):
        """Set a new settings password"""
        dialog = PasswordSetupDialog(self.root)
        if dialog.result:
            password_hash = self.hash_password(dialog.result)
            self.user_preferences['settings_password_hash'] = password_hash
            self.save_preferences()
            messagebox.showinfo("Password Set",
                "Settings password has been set successfully!\n\n"
                "🔒 Your API keys and configuration are now protected.\n"
                "Remember this password - you'll need it to access settings.")
            return True
        return False

    def check_settings_password(self, stored_hash):
        """Check if entered password matches stored hash"""
        dialog = PasswordValidationDialog(self.root)
        if dialog.result:
            entered_hash = self.hash_password(dialog.result)
            if entered_hash == stored_hash:
                return True
            else:
                messagebox.showerror("Access Denied",
                    "❌ Incorrect password!\n\n"
                    "Settings access denied for security.\n"
                    "Please enter the correct settings password.")
                return False
        return False

    def hash_password(self, password):
        """Hash password using SHA-256 with salt"""
        salt = self.user_preferences.get('password_salt', None)
        if salt is None:
            salt = secrets.token_hex(32)
            self.user_preferences['password_salt'] = salt
            self.save_preferences()

        return hashlib.sha256((password + salt).encode()).hexdigest()

    def view_logs(self):
        """Switch to logs tab"""
        self.notebook.select(2)
        self.refresh_logs()

    def clear_cache(self):
        """Clear application cache"""
        try:
            cache_dirs = ['output_files_ict_local', 'previous_analysis_data_ict_local']
            cleared = 0

            for cache_dir in cache_dirs:
                cache_path = os.path.join(current_dir, cache_dir)
                if os.path.exists(cache_path):
                    import shutil
                    shutil.rmtree(cache_path)
                    cleared += 1

            messagebox.showinfo("Cache Cleared", f"Cleared {cleared} cache directories.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to clear cache:\n{e}")

    def show_about(self):
        """Show about dialog"""
        about_text = """
ICT Analyzer GUI v1.0

A comprehensive trading analysis application using Inner Circle Trader (ICT) concepts.

Features:
• Multi-timeframe analysis
• AI-powered evaluation
• Setup tracking
• Real-time monitoring

Developed with enhanced MTF detector awareness and advanced evaluation algorithms.

© 2024 ICT Analyzer Team
        """
        messagebox.showinfo("About ICT Analyzer", about_text.strip())

    def show_user_guide(self):
        """Show user guide"""
        guide_text = """
ICT Analyzer User Guide

1. Symbol Analysis:
   - Enter base and quote currencies
   - Click 'Analyze Symbol' to start analysis
   - View results in the Analysis Results tab

2. Setup Tracking:
   - Click 'Track All Setups' to view open positions
   - Double-click on a setup for details
   - Use 'Export Report' to save data

3. Settings:
   - Adjust AI threshold using the slider
   - Enable/disable auto-Telegram sending
   - Import/export settings via File menu

4. Logs:
   - View application logs in the Logs tab
   - Use 'Refresh' to update log display

For technical support, please check the logs for error details.
        """
        messagebox.showinfo("User Guide", guide_text.strip())

    def on_closing(self):
        """Handle application closing"""
        try:
            self.save_preferences()
            self.logger.info("ICT Analyzer GUI closing")
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
        finally:
            self.root.destroy()


class PasswordSetupDialog:
    """Dialog for setting up settings password"""

    def __init__(self, parent):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔒 Set Settings Password")
        self.dialog.geometry("500x450")  # Increased height to fit all content
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Make sure dialog stays on top
        self.dialog.lift()
        self.dialog.attributes('-topmost', True)

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (450 // 2)
        self.dialog.geometry(f"500x450+{x}+{y}")

        # Wait for dialog to be ready
        self.dialog.after(100, self.create_interface)

    def create_interface(self):
        """Create the password setup interface with guaranteed button visibility"""
        # Create main container with fixed button area
        container = ttk.Frame(self.dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Content area (scrollable if needed)
        content_frame = ttk.Frame(container)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Title and explanation
        title_label = ttk.Label(content_frame, text="🔒 Secure Settings Access",
                               font=('TkDefaultFont', 12, 'bold'))
        title_label.pack(pady=(0, 10))

        explanation = ttk.Label(content_frame,
            text="To protect your valuable API keys and sensitive configuration,\n"
                 "please set a password for accessing the Settings dialog.",
            justify=tk.CENTER, foreground='gray')
        explanation.pack(pady=(0, 15))

        # Password input
        ttk.Label(content_frame, text="Enter Settings Password:",
                 font=('TkDefaultFont', 9, 'bold')).pack(anchor=tk.W, pady=(0, 5))

        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(content_frame, textvariable=self.password_var,
                                       show="*", width=40, font=('TkDefaultFont', 10))
        self.password_entry.pack(pady=(0, 10))

        # Confirm password
        ttk.Label(content_frame, text="Confirm Password:",
                 font=('TkDefaultFont', 9, 'bold')).pack(anchor=tk.W, pady=(0, 5))

        self.confirm_var = tk.StringVar()
        self.confirm_entry = ttk.Entry(content_frame, textvariable=self.confirm_var,
                                      show="*", width=40, font=('TkDefaultFont', 10))
        self.confirm_entry.pack(pady=(0, 10))

        # Show password checkbox
        self.show_var = tk.BooleanVar()
        ttk.Checkbutton(content_frame, text="Show password", variable=self.show_var,
                       command=self.toggle_password_visibility).pack(anchor=tk.W, pady=(0, 15))

        # Instructions
        instructions = ttk.Label(content_frame,
            text="💡 Password must be at least 4 characters",
            justify=tk.CENTER, foreground='blue', font=('TkDefaultFont', 9))
        instructions.pack(pady=(0, 15))

        # FIXED BUTTON AREA - Always visible at bottom
        button_container = ttk.Frame(self.dialog)
        button_container.pack(fill=tk.X, side=tk.BOTTOM, padx=20, pady=(0, 20))

        # Separator line
        separator = ttk.Separator(button_container, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 10))

        # Button frame
        button_frame = ttk.Frame(button_container)
        button_frame.pack(fill=tk.X)

        # Buttons with clear labels and styling
        cancel_btn = ttk.Button(button_frame, text="✖ Cancel", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        set_btn = ttk.Button(button_frame, text="✓ Set Password", command=self.set_password, width=15)
        set_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Status label for feedback
        self.status_label = ttk.Label(button_frame, text="Enter password above and click 'Set Password'",
                                     foreground='gray', font=('TkDefaultFont', 8))
        self.status_label.pack(side=tk.LEFT)

        # Focus on password entry
        self.password_entry.focus()

        # Bind Enter key
        self.dialog.bind('<Return>', lambda e: self.set_password())

        # Remove topmost after setup
        self.dialog.after(1000, lambda: self.dialog.attributes('-topmost', False))

    def toggle_password_visibility(self):
        """Toggle password visibility"""
        show = "" if self.show_var.get() else "*"
        self.password_entry.configure(show=show)
        self.confirm_entry.configure(show=show)

    def set_password(self):
        """Set the password"""
        password = self.password_var.get().strip()
        confirm = self.confirm_var.get().strip()

        if not password:
            messagebox.showerror("Error", "Please enter a password.")
            return

        if len(password) < 4:
            messagebox.showerror("Error", "Password must be at least 4 characters long.")
            return

        if password != confirm:
            messagebox.showerror("Error", "Passwords do not match. Please try again.")
            return

        self.result = password
        self.dialog.destroy()

    def cancel(self):
        """Cancel password setup"""
        self.result = None
        self.dialog.destroy()


class PasswordValidationDialog:
    """Dialog for validating settings password"""

    def __init__(self, parent):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔒 Settings Access")
        self.dialog.geometry("450x250")  # Increased size
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Make sure dialog stays on top
        self.dialog.lift()
        self.dialog.attributes('-topmost', True)

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (250 // 2)
        self.dialog.geometry(f"450x250+{x}+{y}")

        self.create_interface()

    def create_interface(self):
        """Create the password validation interface with guaranteed button visibility"""
        # Create main container with fixed button area
        container = ttk.Frame(self.dialog)
        container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Content area
        content_frame = ttk.Frame(container)
        content_frame.pack(fill=tk.BOTH, expand=True)

        # Title and explanation
        title_label = ttk.Label(content_frame, text="🔒 Protected Settings Access",
                               font=('TkDefaultFont', 11, 'bold'))
        title_label.pack(pady=(0, 10))

        explanation = ttk.Label(content_frame,
            text="Enter your settings password to access\n"
                 "API keys and configuration.",
            justify=tk.CENTER, foreground='gray')
        explanation.pack(pady=(0, 15))

        # Password input
        ttk.Label(content_frame, text="Settings Password:",
                 font=('TkDefaultFont', 9, 'bold')).pack(anchor=tk.W, pady=(0, 5))

        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(content_frame, textvariable=self.password_var,
                                       show="*", width=35, font=('TkDefaultFont', 10))
        self.password_entry.pack(pady=(0, 15))

        # FIXED BUTTON AREA - Always visible at bottom
        button_container = ttk.Frame(self.dialog)
        button_container.pack(fill=tk.X, side=tk.BOTTOM, padx=20, pady=(0, 20))

        # Separator line
        separator = ttk.Separator(button_container, orient='horizontal')
        separator.pack(fill=tk.X, pady=(0, 10))

        # Button frame
        button_frame = ttk.Frame(button_container)
        button_frame.pack(fill=tk.X)

        # Buttons with clear labels
        cancel_btn = ttk.Button(button_frame, text="✖ Cancel", command=self.cancel, width=12)
        cancel_btn.pack(side=tk.RIGHT, padx=(10, 0))

        access_btn = ttk.Button(button_frame, text="✓ Access Settings", command=self.validate, width=15)
        access_btn.pack(side=tk.RIGHT, padx=(0, 10))

        # Status label
        self.status_label = ttk.Label(button_frame, text="Enter your password and click 'Access Settings'",
                                     foreground='gray', font=('TkDefaultFont', 8))
        self.status_label.pack(side=tk.LEFT)

        # Focus on password entry
        self.password_entry.focus()

        # Bind Enter key
        self.dialog.bind('<Return>', lambda e: self.validate())

        # Remove topmost after setup
        self.dialog.after(1000, lambda: self.dialog.attributes('-topmost', False))

    def validate(self):
        """Validate the password"""
        password = self.password_var.get().strip()

        if not password:
            messagebox.showerror("Error", "Please enter your password.")
            return

        self.result = password
        self.dialog.destroy()

    def cancel(self):
        """Cancel password validation"""
        self.result = None
        self.dialog.destroy()


class ModelSelectionDialog:
    """Dialog for selecting LLM model from suggestions"""

    def __init__(self, parent, models):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Select LLM Model")
        self.dialog.geometry("350x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (350 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (400 // 2)
        self.dialog.geometry(f"350x400+{x}+{y}")

        self.create_interface(models)

    def create_interface(self, models):
        """Create the model selection interface"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="🧠 Select LLM Model",
                               font=('TkDefaultFont', 11, 'bold'))
        title_label.pack(pady=(0, 15))

        # Model list
        ttk.Label(main_frame, text="Choose a model:", font=('TkDefaultFont', 9, 'bold')).pack(anchor=tk.W, pady=(0, 10))

        # Create listbox with scrollbar
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.model_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set, font=('TkDefaultFont', 9))
        self.model_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar.config(command=self.model_listbox.yview)

        # Populate listbox
        for model in models:
            self.model_listbox.insert(tk.END, model)

        # Select first item by default
        if models:
            self.model_listbox.selection_set(0)

        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="Cancel", command=self.cancel).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Select", command=self.select_model).pack(side=tk.RIGHT, padx=(0, 10))

        # Bind double-click
        self.model_listbox.bind('<Double-Button-1>', lambda e: self.select_model())

    def select_model(self):
        """Select the chosen model"""
        selection = self.model_listbox.curselection()
        if selection:
            self.result = self.model_listbox.get(selection[0])
        self.dialog.destroy()

    def cancel(self):
        """Cancel model selection"""
        self.result = None
        self.dialog.destroy()


class SettingsDialog:
    """Settings dialog window with password protection and enhanced features"""

    def __init__(self, parent, preferences: Dict, save_callback):
        self.preferences = preferences
        self.save_callback = save_callback

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("🔒 ICT Analyzer Settings (Protected)")
        self.dialog.geometry("650x600")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (650 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"650x600+{x}+{y}")

        self.create_settings_interface()

    def create_settings_interface(self):
        """Create the settings interface"""
        # Create scrollable frame
        canvas = tk.Canvas(self.dialog)
        scrollbar = ttk.Scrollbar(self.dialog, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        main_frame = ttk.Frame(scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Load current settings from config file
        self.load_current_settings()

        # API Settings
        api_frame = ttk.LabelFrame(main_frame, text="🔑 API Configuration", padding="15")
        api_frame.pack(fill=tk.X, pady=(0, 15))

        # CryptoCompare API Key
        ttk.Label(api_frame, text="CryptoCompare API Key:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(api_frame, text="Required for market data. Get free key from: https://cryptocompare.com",
                 foreground='gray').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.cc_api_var = tk.StringVar(value=self.current_settings.get('cryptocompare_api_key', ''))
        cc_entry = ttk.Entry(api_frame, textvariable=self.cc_api_var, width=60, show="*")
        cc_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # Show/Hide button for CryptoCompare key
        self.cc_show_var = tk.BooleanVar()
        ttk.Checkbutton(api_frame, text="Show key", variable=self.cc_show_var,
                       command=lambda: self.toggle_password_visibility(cc_entry, self.cc_show_var)).grid(row=3, column=0, sticky=tk.W, pady=(0, 15))

        # AvalAI API Key
        ttk.Label(api_frame, text="AvalAI API Key:", font=('TkDefaultFont', 9, 'bold')).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(api_frame, text="Optional for enhanced AI analysis. Contact AvalAI for access.",
                 foreground='gray').grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.avalai_api_var = tk.StringVar(value=self.current_settings.get('avalai_api_key', ''))
        avalai_entry = ttk.Entry(api_frame, textvariable=self.avalai_api_var, width=60, show="*")
        avalai_entry.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        # Show/Hide button for AvalAI key
        self.avalai_show_var = tk.BooleanVar()
        ttk.Checkbutton(api_frame, text="Show key", variable=self.avalai_show_var,
                       command=lambda: self.toggle_password_visibility(avalai_entry, self.avalai_show_var)).grid(row=7, column=0, sticky=tk.W, pady=(0, 15))

        # LLM Model Selection (Text Input)
        ttk.Label(api_frame, text="LLM Model for AI Analysis:", font=('TkDefaultFont', 9, 'bold')).grid(row=8, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(api_frame, text="Enter model name manually (e.g., gpt-4, claude-3-opus, gemini-pro, llama-2-70b)",
                 foreground='gray').grid(row=9, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.llm_model_var = tk.StringVar(value=self.current_settings.get('llm_model', 'gpt-4'))
        llm_entry = ttk.Entry(api_frame, textvariable=self.llm_model_var, width=60)
        llm_entry.grid(row=10, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # Popular model suggestions
        suggestions_frame = ttk.Frame(api_frame)
        suggestions_frame.grid(row=11, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Label(suggestions_frame, text="Popular models:", font=('TkDefaultFont', 8), foreground='gray').pack(anchor=tk.W)

        suggestions_text = "gpt-4, gpt-4-turbo, gpt-3.5-turbo, claude-3-opus, claude-3-sonnet, claude-3-haiku, gemini-pro, llama-2-70b"
        suggestions_label = ttk.Label(suggestions_frame, text=suggestions_text,
                                    font=('TkDefaultFont', 8), foreground='blue', cursor="hand2")
        suggestions_label.pack(anchor=tk.W)

        # Make suggestions clickable
        def set_model_from_suggestion(event):
            # Get clicked position and extract model name
            clicked_text = suggestions_label.cget("text")
            models = [m.strip() for m in clicked_text.split(',')]
            # For simplicity, show a selection dialog
            model_dialog = ModelSelectionDialog(self.dialog, models)
            if model_dialog.result:
                self.llm_model_var.set(model_dialog.result)

        suggestions_label.bind("<Button-1>", set_model_from_suggestion)

        # Configure grid weights
        api_frame.columnconfigure(0, weight=1)

        # Telegram Settings
        telegram_frame = ttk.LabelFrame(main_frame, text="📱 Telegram Integration", padding="15")
        telegram_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(telegram_frame, text="Bot Token:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(telegram_frame, text="Get from @BotFather on Telegram", foreground='gray').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.telegram_token_var = tk.StringVar(value=self.current_settings.get('telegram_bot_token', ''))
        telegram_entry = ttk.Entry(telegram_frame, textvariable=self.telegram_token_var, width=60, show="*")
        telegram_entry.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        self.telegram_show_var = tk.BooleanVar()
        ttk.Checkbutton(telegram_frame, text="Show token", variable=self.telegram_show_var,
                       command=lambda: self.toggle_password_visibility(telegram_entry, self.telegram_show_var)).grid(row=3, column=0, sticky=tk.W, pady=(0, 15))

        ttk.Label(telegram_frame, text="Chat ID:", font=('TkDefaultFont', 9, 'bold')).grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(telegram_frame, text="Your Telegram chat ID (send /start to your bot to get it)", foreground='gray').grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.telegram_chat_var = tk.StringVar(value=self.current_settings.get('telegram_chat_id', ''))
        ttk.Entry(telegram_frame, textvariable=self.telegram_chat_var, width=60).grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Label(telegram_frame, text="Admin Chat ID:", font=('TkDefaultFont', 9, 'bold')).grid(row=7, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(telegram_frame, text="Admin chat for detailed reports (optional)", foreground='gray').grid(row=8, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.telegram_admin_var = tk.StringVar(value=self.current_settings.get('telegram_admin_chat_id', ''))
        ttk.Entry(telegram_frame, textvariable=self.telegram_admin_var, width=60).grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        telegram_frame.columnconfigure(0, weight=1)

        # Analysis Settings
        analysis_frame = ttk.LabelFrame(main_frame, text="⚙️ Analysis Configuration", padding="15")
        analysis_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(analysis_frame, text="AI Threshold:", font=('TkDefaultFont', 9, 'bold')).grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        ttk.Label(analysis_frame, text="Minimum score for AI submission (10.0 - 50.0)", foreground='gray').grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        self.threshold_var = tk.DoubleVar(value=self.current_settings.get('ai_threshold', 22.5))
        threshold_frame = ttk.Frame(analysis_frame)
        threshold_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        threshold_scale = ttk.Scale(threshold_frame, from_=10.0, to=50.0, variable=self.threshold_var, orient=tk.HORIZONTAL, length=400)
        threshold_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.threshold_label = ttk.Label(threshold_frame, text=f"{self.threshold_var.get():.1f}")
        self.threshold_label.pack(side=tk.RIGHT, padx=(10, 0))

        # Update label when scale changes
        threshold_scale.configure(command=lambda val: self.threshold_label.configure(text=f"{float(val):.1f}"))

        analysis_frame.columnconfigure(0, weight=1)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Security indicator
        security_frame = ttk.Frame(self.dialog)
        security_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=20, pady=(0, 10))

        security_label = ttk.Label(security_frame,
            text="🔒 Settings are password-protected for security",
            font=('TkDefaultFont', 8), foreground='green')
        security_label.pack(side=tk.LEFT)

        ttk.Button(security_frame, text="Change Password",
                  command=self.change_password).pack(side=tk.RIGHT)

        # Buttons frame (fixed at bottom)
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=20, pady=(0, 20))

        ttk.Button(button_frame, text="Test API Keys", command=self.test_api_keys).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Reset to Defaults", command=self.reset_to_defaults).pack(side=tk.LEFT, padx=(10, 0))
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Save Configuration", command=self.save_settings).pack(side=tk.RIGHT, padx=(0, 10))

    def load_current_settings(self):
        """Load current settings from config file and preferences with proper defaults"""
        self.current_settings = {}

        # Load from config.py with fallback to preferences
        try:
            # Get values from config, but clean up placeholder values
            cc_key = getattr(config, 'CRYPTOCOMPARE_API_KEY', '')
            if cc_key == 'your_cryptocompare_api_key_here':
                cc_key = ''

            avalai_key = getattr(config, 'AVALAI_API_KEY', '')
            if avalai_key == 'your_avalai_api_key_here':
                avalai_key = ''

            telegram_token = getattr(config, 'TELEGRAM_BOT_TOKEN', '')
            if telegram_token == 'your_telegram_bot_token_here':
                telegram_token = ''

            telegram_chat = getattr(config, 'TELEGRAM_CHAT_ID', '')
            if telegram_chat == 'your_telegram_chat_id_here':
                telegram_chat = ''

            # Set cleaned values or fallback to preferences
            self.current_settings['cryptocompare_api_key'] = cc_key or self.preferences.get('cryptocompare_api_key', '')
            self.current_settings['avalai_api_key'] = avalai_key or self.preferences.get('avalai_api_key', '')
            self.current_settings['llm_model'] = getattr(config, 'LLM_MODEL', 'gpt-4') or self.preferences.get('llm_model', 'gpt-4')
            self.current_settings['telegram_bot_token'] = telegram_token or self.preferences.get('telegram_bot_token', '')
            self.current_settings['telegram_chat_id'] = telegram_chat or self.preferences.get('telegram_chat_id', '')
            self.current_settings['telegram_admin_chat_id'] = getattr(config, 'TELEGRAM_ADMIN_CHAT_ID', '') or self.preferences.get('telegram_admin_chat_id', '')
            self.current_settings['ai_threshold'] = getattr(config, 'AI_SEND_THRESHOLD_GLOBAL', 22.5)
        except Exception as e:
            print(f"Error loading config: {e}")
            # Fallback to preferences only
            self.current_settings['cryptocompare_api_key'] = self.preferences.get('cryptocompare_api_key', '')
            self.current_settings['avalai_api_key'] = self.preferences.get('avalai_api_key', '')
            self.current_settings['llm_model'] = self.preferences.get('llm_model', 'gpt-4')
            self.current_settings['telegram_bot_token'] = self.preferences.get('telegram_bot_token', '')
            self.current_settings['telegram_chat_id'] = self.preferences.get('telegram_chat_id', '')
            self.current_settings['telegram_admin_chat_id'] = self.preferences.get('telegram_admin_chat_id', '')
            self.current_settings['ai_threshold'] = self.preferences.get('ai_threshold', 22.5)

    def toggle_password_visibility(self, entry_widget, show_var):
        """Toggle password visibility for entry widgets"""
        if show_var.get():
            entry_widget.configure(show="")
        else:
            entry_widget.configure(show="*")

    def test_api_keys(self):
        """Test API key connectivity"""
        try:
            cc_key = self.cc_api_var.get().strip()
            avalai_key = self.avalai_api_var.get().strip()

            results = []

            # Test CryptoCompare API
            if cc_key:
                try:
                    import requests
                    response = requests.get(f"https://min-api.cryptocompare.com/data/price?fsym=BTC&tsyms=USD&api_key={cc_key}", timeout=10)
                    if response.status_code == 200:
                        results.append("✅ CryptoCompare API: Connected successfully")
                    else:
                        results.append(f"❌ CryptoCompare API: Error {response.status_code}")
                except Exception as e:
                    results.append(f"❌ CryptoCompare API: Connection failed - {str(e)}")
            else:
                results.append("⚠️ CryptoCompare API: No key provided")

            # Test AvalAI API (basic connectivity test)
            if avalai_key:
                results.append("⚠️ AvalAI API: Key provided (full test requires specific endpoint)")
            else:
                results.append("⚠️ AvalAI API: No key provided")

            messagebox.showinfo("API Test Results", "\n".join(results))

        except Exception as e:
            messagebox.showerror("Test Error", f"Failed to test API keys:\n{e}")

    def change_password(self):
        """Change the settings password"""
        dialog = PasswordSetupDialog(self.dialog)
        if dialog.result:
            # Hash and save new password
            password_hash = self.hash_password(dialog.result)
            self.preferences['settings_password_hash'] = password_hash
            self.save_callback()
            messagebox.showinfo("Password Changed",
                "Settings password has been changed successfully!\n\n"
                "🔒 Your API keys remain protected with the new password.")

    def hash_password(self, password):
        """Hash password using SHA-256 with salt"""
        salt = self.preferences.get('password_salt', None)
        if salt is None:
            import secrets
            salt = secrets.token_hex(32)
            self.preferences['password_salt'] = salt
            self.save_callback()

        import hashlib
        return hashlib.sha256((password + salt).encode()).hexdigest()

    def reset_to_defaults(self):
        """Reset all settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            self.cc_api_var.set("")
            self.avalai_api_var.set("")
            self.llm_model_var.set("gpt-4")
            self.telegram_token_var.set("")
            self.telegram_chat_var.set("")
            self.telegram_admin_var.set("")
            self.threshold_var.set(22.5)
            self.threshold_label.configure(text="22.5")

    def save_settings(self):
        """Save the settings to config file and preferences"""
        try:
            # Validate required fields
            cc_key = self.cc_api_var.get().strip()
            if not cc_key:
                if not messagebox.askyesno("Missing API Key",
                    "CryptoCompare API key is required for analysis. Continue without it?\n\n"
                    "You can get a free key from: https://cryptocompare.com"):
                    return

            # Get LLM model value (now direct text input)
            llm_model_value = self.llm_model_var.get().strip() or 'gpt-4'

            # Prepare settings to save
            settings_to_save = {
                'CRYPTOCOMPARE_API_KEY': cc_key,
                'AVALAI_API_KEY': self.avalai_api_var.get().strip(),
                'LLM_MODEL': llm_model_value,
                'TELEGRAM_BOT_TOKEN': self.telegram_token_var.get().strip(),
                'TELEGRAM_CHAT_ID': self.telegram_chat_var.get().strip(),
                'TELEGRAM_ADMIN_CHAT_ID': self.telegram_admin_var.get().strip(),
                'AI_SEND_THRESHOLD_GLOBAL': self.threshold_var.get(),
            }

            # Save to config.py
            self.save_to_config_file(settings_to_save)

            # Update preferences
            self.preferences['ai_threshold'] = self.threshold_var.get()
            self.preferences['cryptocompare_api_key'] = cc_key
            self.preferences['avalai_api_key'] = self.avalai_api_var.get().strip()
            self.preferences['llm_model'] = llm_model_value
            self.preferences['telegram_bot_token'] = self.telegram_token_var.get().strip()
            self.preferences['telegram_chat_id'] = self.telegram_chat_var.get().strip()
            self.preferences['telegram_admin_chat_id'] = self.telegram_admin_var.get().strip()

            # Save preferences
            self.save_callback()

            # Update config module in memory
            for key, value in settings_to_save.items():
                setattr(config, key, value)

            messagebox.showinfo("Success",
                "Settings saved successfully!\n\n"
                "✅ Configuration file updated\n"
                "✅ Preferences saved\n"
                "✅ Settings active immediately\n\n"
                "You can now run analysis with your configured settings.")

            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings:\n{e}")

    def save_to_config_file(self, settings):
        """Save settings to config.py file"""
        try:
            config_file_path = os.path.join(os.path.dirname(__file__), 'config.py')

            # Read current config file
            if os.path.exists(config_file_path):
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    config_content = f.read()
            else:
                config_content = "# ICT Analyzer Configuration\n\n"

            # Update or add each setting
            for key, value in settings.items():
                if isinstance(value, str):
                    new_line = f'{key} = "{value}"\n'
                else:
                    new_line = f'{key} = {value}\n'

                # Check if setting already exists
                import re
                pattern = rf'^{re.escape(key)}\s*=.*$'
                if re.search(pattern, config_content, re.MULTILINE):
                    # Replace existing setting
                    config_content = re.sub(pattern, new_line.rstrip(), config_content, flags=re.MULTILINE)
                else:
                    # Add new setting
                    config_content += f"\n{new_line}"

            # Write updated config
            with open(config_file_path, 'w', encoding='utf-8') as f:
                f.write(config_content)

        except Exception as e:
            raise Exception(f"Failed to update config file: {e}")


def main():
    """Main function to run the GUI application"""
    # Create the main window
    root = tk.Tk()

    # Create and run the application
    app = ICTAnalyzerGUI(root)

    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    # Start the GUI event loop
    root.mainloop()


if __name__ == "__main__":
    main()
