2025-05-31 02:09:40,343 - ICTAnalyzerGUI - INFO - ICT Analyzer GUI started
2025-05-31 02:09:41,047 - chart_agent_module - INFO - --- ChartAgent instance created (v3.4 - with refined EVAL params & AI confidence handling) ---
2025-05-31 02:09:41,048 - config - INFO - Ticker globals updated: BTC-USD
2025-05-31 02:09:41,048 - chart_agent_module - INFO - Currency pair set to: BTC-USD
2025-05-31 02:09:41,048 - chart_agent_module - INFO - Plotting config loaded: Annotation Level='compact'
2025-05-31 02:09:41,048 - ICTAnalyzerGUI - INFO - Status: Chart Agent initialized successfully
2025-05-31 02:13:13,790 - config - INFO - config.py fully updated with REFINED EVAL_, PLOTTING, AI RETRY parameters, and ENHANCED AI prompt.
2025-05-31 02:13:13,790 - config - INFO - CRYPTOCOMPARE_API_KEY: Set
2025-05-31 02:13:13,790 - config - INFO - AVALAI_API_KEY: Set
2025-05-31 02:13:13,790 - config - INFO - TELEGRAM_BOT_TOKEN: Set
2025-05-31 02:13:59,266 - config - INFO - config.py fully updated with REFINED EVAL_, PLOTTING, AI RETRY parameters, and ENHANCED AI prompt.
2025-05-31 02:13:59,267 - config - INFO - CRYPTOCOMPARE_API_KEY: Set
2025-05-31 02:13:59,267 - config - INFO - AVALAI_API_KEY: Set
2025-05-31 02:13:59,267 - config - INFO - TELEGRAM_BOT_TOKEN: Set
2025-05-31 02:14:04,927 - ICTAnalyzerGUI - INFO - ICT Analyzer GUI closing
