2025-05-31 02:20:59,053 - ICTAnalyzerGUI - INFO - ICT Analyzer GUI started
2025-05-31 02:20:59,770 - chart_agent_module - INFO - --- ChartAgent instance created (v3.4 - with refined EVAL params & AI confidence handling) ---
2025-05-31 02:20:59,770 - config - INFO - Ticker globals updated: BTC-USD
2025-05-31 02:20:59,770 - chart_agent_module - INFO - Currency pair set to: BTC-USD
2025-05-31 02:20:59,770 - chart_agent_module - INFO - Plotting config loaded: Annotation Level='compact'
2025-05-31 02:20:59,770 - ICTAnalyzerGUI - INFO - Status: Chart Agent initialized successfully
2025-05-31 02:23:08,453 - config - INFO - config.py fully updated with REFINED EVAL_, PLOTTING, AI RETRY parameters, and ENHANCED AI prompt.
2025-05-31 02:23:08,453 - config - INFO - CRYPTOCOMPARE_API_KEY: Set
2025-05-31 02:23:08,453 - config - INFO - AVALAI_API_KEY: Set
2025-05-31 02:23:08,453 - config - INFO - TELEGRAM_BOT_TOKEN: Set
2025-05-31 02:23:51,511 - ICTAnalyzerGUI - INFO - ICT Analyzer GUI closing
