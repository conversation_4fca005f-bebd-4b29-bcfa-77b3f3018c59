# ICT Analyzer GUI Requirements
# Core GUI framework (included with Python)
# tkinter - included with Python standard library

# Data processing and analysis
pandas>=1.5.0
numpy>=1.21.0

# HTTP requests and API calls
requests>=2.28.0
aiohttp>=3.8.0

# Date and time handling
python-dateutil>=2.8.0

# Plotting and visualization
matplotlib>=3.5.0
plotly>=5.10.0

# Async support
asyncio-throttle>=1.0.0

# JSON and data serialization (included with Python)
# json - included with Python standard library

# File operations and utilities (included with Python)
# os, sys, threading, queue - included with Python standard library

# Optional: For enhanced GUI features
# Pillow>=9.0.0  # For image processing if needed
# ttkthemes>=3.2.0  # For additional GUI themes

# Development and testing (optional)
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0
